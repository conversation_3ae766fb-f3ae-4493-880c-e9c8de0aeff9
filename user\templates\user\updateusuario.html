{% extends 'BaseAdmin/base.html' %}
{% load static %}

{% block content %}
<br><br>
<div class="container">

  <a href="{% url 'ListaUser' %}" class="btn btn-danger"> Regresar </a>
</div>
<main class="col-md-9 ms-sm-auto col-lg-12 px-md-4" style="padding-top: 20px;"><br><br>
    <div class="container"  style="background-color:white;">
        <form action="" method="POST" enctype="multipart/form-data">{% csrf_token %}
            <div class="container overflow-hidden">
                <h2 align="center" class="text-primary">Modificacion de Usuario: <strong class="text-danger">{{d}}</strong></h2>
                <hr><br>
                <div class="row gy-5">
                    {{form.as_p}}
                </div>
            </div>

            <div class="container" align="center">
                <button class="btn btn-success" type="submit">Modificar</button>
                <button class="btn btn-danger" type="reset">Cancelar</button>
            </div>
            <br>
        </form>
    </div>
</main>
{% endblock %}