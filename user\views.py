from django.shortcuts import render,redirect
from user.models import User
from user.forms import RegistroForm
from django.contrib import messages
from django.contrib.auth.hashers import make_password
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from Cliente.models import Cliente
from datetime import datetime
import uuid  

@login_required
def usuarios(request):
    if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
    else:
        #paginacion 
        comments = User.objects.all() 
        paginator = Paginator(comments, 10) # Show 25 contacts per page.
 
        page_number = request.GET.get('page')
        comments_page = paginator.get_page(page_number)

        return render(request,'user/todousuario.html',{'comments_page': comments_page,'categoria':comments})
        
        
@login_required
def nuevousuario(request):
    if not request.user.is_authenticated and not request.user.is_active and request.user.rol == 'admin':
        return redirect('/')
    else:
        form = RegistroForm()
        if request.method == "POST":
            form = RegistroForm(request.POST)
            if form.is_valid():
                try:
                    u = User()
                    u.username = form.cleaned_data["username"]
                    u.password = make_password(form.cleaned_data["password"])
                    u.first_name = form.cleaned_data["first_name"]
                    u.last_name = form.cleaned_data["last_name"]
                    u.email = form.cleaned_data["email"]
                    u.rol = form.cleaned_data["rol"]
                    u.is_staff = form.cleaned_data["is_staff"]
                    u.is_active = form.cleaned_data["is_active"]
                    u.is_superuser = form.cleaned_data["is_superuser"]
                    u.save()
                    messages.success(request,'Registro de Usuario Exitoso')
                    return redirect('ListaUser')
                except:
                    messages.error(request,'Registro de Usuario Fallido')
                    return redirect('NuevoUser')

            else:
                messages.error(request,"Formulario Corrupto")
                return redirect('NuevoUser')


        return render(request,"user/nuevousuario.html",{'form':form})


def login_signup(request):
    if request.method == 'POST':
               #S try:
                    u = User()
                    u.username = request.POST["username"]
                    u.password = make_password(request.POST["password"])
                    u.first_name = request.POST["first_name"]
                    u.last_name = request.POST["last_name"]
                    u.email = request.POST["email"]
                    u.rol = "cliente"
                    u.is_staff = 0
                    u.is_active = 1
                    u.is_superuser = 0
                    u.estado = 1
                    u.token = uuid.uuid4()
                    u.save()
                    nuevocliente (u.token, request.POST["nit"],request.POST["fecha_nac"],request.POST["direccion"],request.POST["telefono"])
                    messages.success(request,'Registro de Usuario Exitoso')
                    return redirect('/')
               # except:
                    #messages.error(request,'Registro de Usuario Fallido')
                    #return redirect('/')
        
    return render(request,"user/signup.html")


def nuevocliente(t,nit,nac,dir,tel):
    usuario = User.objects.get(token=t)

    c = Cliente()
    c.tipo = 'cliente'
    c.nombre = usuario.first_name
    c.apellido = usuario.last_name
    c.nit = nit
    c.fecha_nac = nac
    c.direccion = dir
    c.telefono = tel
    c.compras = 0
    c.total_compras = 0.00
    c.devoluciones = 0
    c.total_devoluciones = 0.00
    c.descuento_empleado = 0
    c.estado = 1
    c.usuario = User.objects.get(id = usuario.id)
    c.fecha = datetime.today()
    c.token = t
    c.save()
    return redirect('/')

@login_required
def listausuario(request):
    if not request.user.is_authenticated and not request.user.is_active and request.user.rol == 'admin':
        return redirect('/')
    else:
        usuarios = User.objects.all()
        return render(request,"user/todousuario.html",{'usuarios':usuarios})

def listausuario(request):
    # Verificar si el usuario está autenticado, activo y tiene rol de admin
    if not request.user.is_authenticated or not request.user.is_active or request.user.rol != 'admin':
        return redirect('/')

    # Filtrar usuarios por rol de 'cliente' y que estén activos
    usuarios = User.objects.all()
    
    return render(request, "user/todousuario.html", {'usuarios': usuarios})


def listatrabajador(request):
    # Verificar si el usuario está autenticado, activo y tiene rol de admin
    if not request.user.is_authenticated or not request.user.is_active or request.user.rol != 'admin':
        return redirect('/')

    # Filtrar usuarios por rol de 'cliente' y que estén activos
    usuarios = User.objects.filter(is_active=True, rol='trabajador')
    
    return render(request, "user/todousuario.html", {'usuarios': usuarios})


def listausuario2(request):
    if not request.user.is_authenticated and not request.user.is_active and request.user.rol == 'admin':
        return redirect('/')
    else:
        usuarios = User.objects.filter(is_active=0)
        return render(request,"user/todousuario2.html",{'usuarios':usuarios})

@login_required
def updateusuario(request,id):
    if not request.user.is_authenticated and not request.user.is_active and request.user.rol == 'admin':
        return redirect('/')
    else:
         usuarios = User.objects.get(username=id)
         if request.method == 'GET':
            form = RegistroForm(instance=usuarios)
         else:
            form = RegistroForm(request.POST,request.FILES,instance = usuarios)
     
            if form.is_valid():
                try:
                    form.save()
                    messages.success(request, 'Usuario Modificado Exitosamente!.')
                    return redirect('ListaUser')
                except:
                    messages.error(request, 'Modificacion de Usuario Fallido!.')
                    return redirect('ListaUser')

    
    return render(request,"user/updateusuario.html",{'form':form,'d':id})


@login_required
def deleteusuario(request,id):
    if not request.user.is_authenticated and not request.user.is_active and request.user.rol == 'admin':
        return redirect('/')
    else:
        usuarios = User.objects.get(username=id)
        print(usuarios.username)
        if usuarios.username == request.user:
            messages.error(request, 'No Puedes Eliminar Tu Propio Usuario!.')
            return redirect('ListaUser')          
         
        else:
            if request.method == 'GET':
                try:
                    usuarios.delete() 
                    messages.success(request, 'Usuario Eliminado Exitosamente!.')
                    return redirect('ListaUser')
                except:
                    messages.error(request, 'Eliminacion de Usuario Fallido!.')
                    return redirect('ListaUser')
            else:
              pass           
  




