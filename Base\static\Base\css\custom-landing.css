/* Custom styles for professional landing page */

/* Hero Section Improvements */
.hero {
    position: relative;
    z-index: 1;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-content {
    z-index: 2;
    position: relative;
}

.hero-image img {
    max-height: 500px;
    object-fit: cover;
}

.hero-stats .stat-item {
    padding: 15px;
}

.hero-buttons .btn {
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Testimonials Carousel */
.testimonials-carousel.owl-carousel .owl-item {
    padding: 0 15px;
}

.testimonial-card {
    border: none;
    transition: all 0.3s ease;
    height: 100%;
}

.testimonial-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.1) !important;
}

.testimonial-header h4 {
    font-size: 1.1rem;
    font-weight: 600;
}

.stars {
    font-size: 0.9rem;
}

.author-avatar {
    font-size: 1.2rem;
}

.testimonial-content p {
    font-size: 1rem;
    line-height: 1.6;
}

/* WhatsApp Float Button */
.whatsapp-float {
    position: fixed;
    width: 60px;
    height: 60px;
    bottom: 20px;
    right: 20px;
    background-color: #25d366;
    color: #FFF;
    border-radius: 50px;
    text-align: center;
    font-size: 30px;
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.4);
    z-index: 1001;
    transition: all 0.3s ease;
}

.whatsapp-float:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(37, 211, 102, 0.6);
}

.whatsapp-btn {
    width: 60px;
    height: 60px;
    line-height: 66px;
    color: #fff;
    text-decoration: none;
    display: block;
}

.whatsapp-btn:hover {
    color: #fff;
    text-decoration: none;
}

/* Mobile WhatsApp positioning */
@media (max-width: 768px) {
    .whatsapp-float {
        width: 50px;
        height: 50px;
        bottom: 15px;
        right: 15px;
        font-size: 24px;
    }

    .whatsapp-btn {
        width: 50px;
        height: 50px;
        line-height: 56px;
    }
}

/* Benefit boxes */
.benefit-box {
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}

.benefit-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.benefit-box h3 {
    color: #333;
    font-weight: 600;
}

.benefit-box p {
    color: #666;
}

/* Section backgrounds and text colors */
#benefits-section {
    background-color: #f8f9fa !important;
}

#benefits-section h1,
#benefits-section h2 {
    color: #333 !important;
}

#benefits-section p {
    color: #666 !important;
}

#projects-section {
    background-color: #f8f9fa !important;
}

#projects-section h1,
#projects-section h2 {
    color: #333 !important;
}

#projects-section p {
    color: #666 !important;
}

#process-section {
    background-color: #f8f9fa !important;
}

#process-section h1,
#process-section h2 {
    color: #333 !important;
}

#process-section p {
    color: #666 !important;
}

/* Service cards */
.services-1 {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.4s ease;
    height: 100%;
    display: block;
    text-decoration: none;
    color: #333;
    padding: 40px 30px;
    text-align: center;
    border: 2px solid transparent;
}

.services-1:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    text-decoration: none;
    color: #333;
    background: white;
    border-color: #007bff;
}

.services-1 .icon {
    color: #007bff !important;
    margin-bottom: 25px;
    display: block;
    transition: all 0.3s ease;
}

.services-1:hover .icon {
    color: #007bff !important;
    transform: scale(1.2);
}

.services-1 .icon i {
    color: #007bff !important;
}

.services-1:hover .icon i {
    color: #007bff !important;
}

.services-1 h3 {
    color: #333 !important;
    font-weight: 600;
    margin-bottom: 15px;
}

.services-1 p {
    color: #666 !important;
    margin-bottom: 25px;
    line-height: 1.6;
}

.services-1 .btn {
    opacity: 1;
    visibility: visible;
    position: relative;
    z-index: 10;
    font-weight: 600;
}

.services-1:hover .btn {
    opacity: 1;
    visibility: visible;
    background-color: #007bff;
    border-color: #007bff;
}

/* Project cards */
.project-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}

.project-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.project-img img {
    height: 200px;
    object-fit: cover;
}

.result {
    font-size: 0.9em;
}

/* Testimonial cards */
.testimonial-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}

.testimonial-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.1);
}

/* Process steps */
.process-step {
    transition: transform 0.3s ease;
}

.process-step:hover {
    transform: translateY(-3px);
}

.step-number {
    font-weight: bold;
}

/* Contact form */
.contact-form {
    transition: box-shadow 0.3s ease;
}

.contact-form:hover {
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.contact-item {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.contact-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.1);
}

/* Trust items */
.trust-item {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.trust-item:last-child {
    border-bottom: none;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .whatsapp-float {
        width: 50px;
        height: 50px;
        bottom: 20px;
        right: 20px;
        font-size: 24px;
    }
    
    .whatsapp-btn {
        width: 50px;
        height: 50px;
        line-height: 56px;
    }
    
    .hero h1 {
        font-size: 2rem;
    }
    
    .hero h2 {
        font-size: 1.2rem;
    }
}

/* Button improvements */
.btn-success:hover {
    background-color: #1e7e34;
    border-color: #1c7430;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #004085;
}

/* Form improvements */
.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Navbar CTA button */
.navbar .btn-success {
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.navbar .btn-success:hover {
    transform: scale(1.05);
}

/* Process steps styling */
.process-step h4 {
    color: #333;
    font-weight: 600;
}

.process-step p {
    color: #666;
}

/* Testimonials styling */
.testimonial-card h5 {
    color: #333;
    font-weight: 600;
}

.testimonial-card p {
    color: #666;
}

/* FAQ styling */
#faq-section h1,
#faq-section h2 {
    color: #333 !important;
}

#faq-section p {
    color: #666 !important;
}

.card-header button {
    color: #333;
    text-decoration: none;
    font-weight: 600;
}

.card-body {
    color: #666;
}

/* Contact section improvements */
#contact-section h1,
#contact-section h2 {
    color: #333 !important;
}

#contact-section p {
    color: #666 !important;
}

.contact-form h3 {
    color: #333;
}

.contact-item h4 {
    color: #333;
}

.contact-item p {
    color: #666;
}

/* About section text colors */
#about-section .heading-section h1,
#about-section .heading-section h2 {
    color: #fff !important;
}

#about-section .heading-section p {
    color: #ddd !important;
}

.trust-item {
    color: #ddd;
}

.trust-item strong {
    color: #fff;
}

.trust-item a {
    color: #007bff;
}

/* General text improvements */
.lead {
    font-size: 1.1rem;
    font-weight: 400;
}

/* Button consistency */
.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    font-weight: 600;
    padding: 10px 20px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #004085;
    transform: translateY(-1px);
}

/* Navbar improvements */
.navbar {
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.navbar-brand strong {
    font-size: 1.5rem;
    color: #fff;
}

/* Carousel navigation - Discrete dots only */
.testimonials-carousel .owl-nav,
.projects-carousel .owl-nav {
    display: none !important;
}

.testimonials-carousel .owl-dots,
.projects-carousel .owl-dots {
    text-align: center;
    margin-top: 30px;
}

.testimonials-carousel .owl-dots .owl-dot,
.projects-carousel .owl-dots .owl-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255,255,255,0.4);
    margin: 0 8px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.testimonials-carousel .owl-dots .owl-dot:hover,
.projects-carousel .owl-dots .owl-dot:hover {
    background: rgba(255,255,255,0.7);
    transform: scale(1.2);
}

.testimonials-carousel .owl-dots .owl-dot.active,
.projects-carousel .owl-dots .owl-dot.active {
    background: #fff;
    border-color: #007bff;
    transform: scale(1.3);
}

/* Projects carousel specific */
.projects-carousel .owl-dots .owl-dot {
    background: rgba(0,123,255,0.3);
}

.projects-carousel .owl-dots .owl-dot.active {
    background: #007bff;
    border-color: #fff;
}

/* Hero section z-index fix */
#home-section {
    position: relative;
    z-index: 1;
}

.hero .home-slider {
    z-index: 1;
}

.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    background: rgba(0,0,0,0.8);
}

/* Ensure hero content is below navbar */
.hero .slider-item {
    z-index: 1;
}

.hero .container {
    position: relative;
    z-index: 2;
}

/* Section spacing improvements */
section {
    padding: 80px 0;
}

#home-section {
    padding-top: 120px;
}

/* Card hover effects */
.project-card,
.benefit-box,
.services-1 {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.project-card:hover,
.benefit-box:hover,
.services-1:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

/* Trust section */
#trust-section .trust-item {
    transition: all 0.3s ease;
    height: 100%;
}

#trust-section .trust-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

#trust-section .trust-item h4 {
    color: #333 !important;
}

#trust-section .trust-item p {
    color: #666 !important;
}

/* Professional spacing and typography */
.display-5 {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.2;
}

.lead {
    font-size: 1.25rem;
    font-weight: 300;
    line-height: 1.6;
}

/* Button improvements */
.btn-lg {
    padding: 15px 30px;
    font-size: 1.1rem;
    border-radius: 8px;
}

.btn-outline-light:hover {
    background-color: #fff;
    color: #007bff;
    border-color: #fff;
}

/* Mobile Navigation Improvements */
@media (max-width: 991px) {
    .navbar-collapse {
        position: fixed;
        top: 0;
        right: -100%;
        width: 300px;
        height: 100vh;
        background: rgba(0,0,0,0.95);
        backdrop-filter: blur(10px);
        transition: right 0.3s ease;
        padding: 80px 20px 20px;
        z-index: 1002;
    }

    .navbar-collapse.show {
        right: 0;
    }

    .navbar-nav {
        flex-direction: column;
        width: 100%;
    }

    .navbar-nav .nav-item {
        margin: 10px 0;
        width: 100%;
    }

    .navbar-nav .nav-link {
        color: #fff !important;
        font-size: 1.1rem;
        padding: 15px 20px;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .navbar-nav .nav-link:hover {
        background: rgba(0,123,255,0.2);
        transform: translateX(10px);
    }

    .navbar-toggler {
        border: none;
        padding: 8px 12px;
        z-index: 1003;
    }

    .navbar-toggler:focus {
        box-shadow: none;
    }

    .navbar-brand strong {
        font-size: 1.3rem;
    }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .display-5 {
        font-size: 2rem;
    }

    .hero-buttons .btn {
        display: block;
        width: 100%;
        margin-bottom: 10px;
    }

    .navbar-brand strong {
        font-size: 1.2rem;
    }

    /* Hero section mobile */
    .hero .container {
        padding-top: 100px !important;
    }

    .hero h1 {
        font-size: 1.8rem;
    }

    .hero h2 {
        font-size: 1.1rem;
    }

    /* Carousel mobile improvements */
    .testimonials-carousel.owl-carousel .owl-item,
    .projects-carousel.owl-carousel .owl-item {
        padding: 0 10px;
    }

    .testimonial-card,
    .project-card {
        margin: 0 5px;
    }

    /* Services mobile */
    .services-1 {
        margin-bottom: 30px;
        padding: 30px 20px;
    }

    /* Trust section mobile */
    #trust-section .trust-item {
        margin-bottom: 20px;
    }
}
