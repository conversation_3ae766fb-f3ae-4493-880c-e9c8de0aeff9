{% extends 'BaseAdmin/base.html' %}
{% load static %}
{% block content %}
{% if messages %}
{% for message in messages %}
<script>
  Swal.fire({
    "title": "Informacion Sistema",
    "text": "{{message}}",
    "icon": "{{message.tags}}"
  })
</script>
{% endfor %}
{% endif %}
<br><br>
<div class="container">
  <a href="#" class="btn btn-info">Nuevo Cliente</a>

  <a href="{% url 'ListaCliente2' %}" class="btn btn-danger"> Clientes Inactivo</a>
</div>
<main class="col-md-9 ms-sm-auto col-lg-12 px-md-4" style="padding-top: 20px;">
  <div class="container" style="background-color:white;"><br><br>
    <h4 align="center" class="text-primary">LISTADO DE USUARIOS</h4>
    <br>
    <table class="table table-hover table-bordered table-sm">
      <thead align="center">
        <tr>
          <th scope="col">Usuario</th>
          <th scope="col">Nombres de Usuario</th>
          <th scope="col">Rol de Sistema</th>
          <th scope="col">Acceso a Panel</th>
          <th scope="col">Privilegios Super Usuario</th>
          <th scope="col">Activo</th>
          <th scope="col">Accion</th>
        </tr>
      </thead>
      <tbody align="center">
        {% for u in usuarios %}
        <tr>
          <th scope="row">{{u.username}}</th>
          <td>{{u.first_name}} {{u.last_name}}</td>
          <td>{{u.rol}}</td>
          {% if u.is_staff == True %}
          <td class="table-success">Si</td>
          {% else %}
          <td class="table-danger">No</td>
          {% endif %}
          {% if u.is_superuser == True %}
          <td class="table-success">Si</td>
          {% else %}
          <td class="table-danger">No</td>
          {% endif %}
          {% if u.is_active == True %}
          <td class="table-success">Si</td>
          {% else %}
          <td class="table-danger">No</td>
          {% endif %}
          <td>
            <a class="btn btn-success btn-sm" title="Modificar" type="button"
              href="{% url 'UpdateUser' u.username %}"><i class="mdi mdi-grease-pencil" aria-hidden="true"></i>Modificar</a>
            <a class="btn btn-danger btn-sm" title="Eliminar" type="button" href="{% url 'DeleteUser' u.username %}"><i
                class="mdi mdi-delete" aria-hidden="true">Eliminar</i></a>
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table><br>
  </div>
</main>
<br><br><br><br>
{% endblock %}