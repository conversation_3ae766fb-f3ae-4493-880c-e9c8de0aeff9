Not Found: /static/Base/css/style.css
Not Found: /static/Base/js/jquery.min.js
Not Found: /static/Base/js/bootstrap.min.js
Not Found: /static/Base/js/main.js
Not Found: /static/Base/js/popper.js
Not Found: /static/Base/images/bg2.jpg
Internal Server Error: /
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/db/backends/mysql/base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/pymysql/cursors.py", line 148, in execute
    result = self._query(query)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/pymysql/cursors.py", line 310, in _query
    conn.query(q)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/pymysql/connections.py", line 548, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/pymysql/connections.py", line 775, in _read_query_result
    result.read()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/pymysql/connections.py", line 1156, in read
    first_packet = self.connection._read_packet()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/pymysql/connections.py", line 725, in _read_packet
    packet.raise_for_error()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1146, "Table 'rayitode_codespa.user_user' doesn't exist")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home3/rayitode/codespa/Login/views.py", line 12, in login_in
    if form.is_valid():
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/forms/forms.py", line 205, in is_valid
    return self.is_bound and not self.errors
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/forms/forms.py", line 200, in errors
    self.full_clean()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/forms/forms.py", line 438, in full_clean
    self._clean_form()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/forms/forms.py", line 459, in _clean_form
    cleaned_data = self.clean()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/contrib/auth/forms.py", line 217, in clean
    self.user_cache = authenticate(
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/views/decorators/debug.py", line 42, in sensitive_variables_wrapper
    return func(*func_args, **func_kwargs)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/contrib/auth/__init__.py", line 77, in authenticate
    user = backend.authenticate(request, **credentials)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/contrib/auth/backends.py", line 46, in authenticate
    user = UserModel._default_manager.get_by_natural_key(username)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/contrib/auth/base_user.py", line 46, in get_by_natural_key
    return self.get(**{self.model.USERNAME_FIELD: username})
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/db/models/manager.py", line 85, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/db/models/query.py", line 646, in get
    num = len(clone)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/db/models/query.py", line 376, in __len__
    self._fetch_all()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/db/models/query.py", line 1867, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/db/models/query.py", line 87, in __iter__
    results = compiler.execute_sql(
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/db/models/sql/compiler.py", line 1398, in execute_sql
    cursor.execute(sql, params)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/db/backends/mysql/base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/pymysql/cursors.py", line 148, in execute
    result = self._query(query)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/pymysql/cursors.py", line 310, in _query
    conn.query(q)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/pymysql/connections.py", line 548, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/pymysql/connections.py", line 775, in _read_query_result
    result.read()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/pymysql/connections.py", line 1156, in read
    first_packet = self.connection._read_packet()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/pymysql/connections.py", line 725, in _read_packet
    packet.raise_for_error()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
django.db.utils.ProgrammingError: (1146, "Table 'rayitode_codespa.user_user' doesn't exist")
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /JkUbxMdITlH
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info'. You may need to add 'www.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info'. You may need to add 'www.codespa.info' to ALLOWED_HOSTS.
Bad Request: /
Invalid HTTP_HOST header: 'www.codespa.info'. You may need to add 'www.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info'. You may need to add 'www.codespa.info' to ALLOWED_HOSTS.
Bad Request: /
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /
Not Found: /favicon.ico
Not Found: /static/BaseSO/assets/vendor/bootstrap/css/bootstrap.min.css
Not Found: /static/BaseSO/assets/css/style.css
Not Found: /static/BaseSO/assets/vendor/bootstrap-icons/bootstrap-icons.css
Not Found: /static/BaseSO/assets/vendor/boxicons/css/boxicons.min.css
Not Found: /static/BaseSO/assets/js/main.js
Not Found: /static/BaseSO/assets/vendor/bootstrap/js/bootstrap.bundle.min.js
Not Found: /static/BaseSO/assets/img/logo.png
Not Found: /static/BaseSO/assets/js/main.js
Not Found: /static/BaseSO/assets/img/apple-touch-icon.png
Not Found: /static/BaseSO/assets/img/favicon.png
Not Found: /static/BaseSO/assets/vendor/bootstrap/css/bootstrap.min.css
Not Found: /static/BaseSO/assets/vendor/boxicons/css/boxicons.min.css
Not Found: /static/BaseSO/assets/css/style.css
Not Found: /static/BaseSO/assets/vendor/bootstrap/js/bootstrap.bundle.min.js
Not Found: /static/BaseSO/assets/vendor/bootstrap-icons/bootstrap-icons.css
Not Found: /static/BaseSO/assets/js/main.js
Not Found: /static/BaseSO/assets/img/logo.png
Not Found: /static/BaseSO/assets/vendor/bootstrap/css/bootstrap.min.css
Not Found: /static/BaseSO/assets/js/main.js
Not Found: /static/BaseSO/assets/vendor/bootstrap/js/bootstrap.bundle.min.js
Not Found: /static/BaseSO/assets/vendor/boxicons/css/boxicons.min.css
Not Found: /static/BaseSO/assets/vendor/bootstrap-icons/bootstrap-icons.css
Not Found: /static/BaseSO/assets/css/style.css
Not Found: /static/BaseSO/assets/img/logo.png
Not Found: /static/BaseSO/assets/js/main.js
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /
Not Found: /favicon.ico
Not Found: /robots.txt
Not Found: /robots.txt
Not Found: /robots.txt
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /robots.txt
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /robots.txt
Not Found: /humans.txt
Not Found: /ads.txt
Not Found: /robots.txt
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /favicon.png
Not Found: /favicon.png
Not Found: /favicon.png
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /index.html
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /index.html
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /favicon.png
Not Found: /favicon.png
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /
Not Found: /favicon.ico
Not Found: /robots.txt
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /robots.txt
Not Found: /archivarix.cms.php
Not Found: /robots.txt
Not Found: /robots.txt
Not Found: /favicon.ico
Not Found: /ads.txt
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /favicon.ico
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Not Found: /robots.txt
Not Found: /sitemap.xml
Not Found: /ads.txt
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /oQPwsQlxSKHV
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /favicon.ico
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Not Found: /robots.txt
Not Found: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /robots.txt
Not Found: /main.js
Not Found: /robots.txt
Not Found: /favicon.ico
Not Found: /robots.txt
Not Found: /robots.txt
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /z0f76a1d14fd21a8fb5fd0d03e0fdc3d3cedae52f
Not Found: /lJZyZMmFWIjQ
Not Found: /xmlrpc.php
Not Found: /robots.txt
Not Found: /favicon.ico
Not Found: /robots.txt
Not Found: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Not Found: /favicon.ico
Not Found: /wp-includes/wlwmanifest.xml
Not Found: /xmlrpc.php
Not Found: /blog/wp-includes/wlwmanifest.xml
Not Found: /wordpress/wp-includes/wlwmanifest.xml
Not Found: /wp/wp-includes/wlwmanifest.xml
Not Found: /site/wp-includes/wlwmanifest.xml
Not Found: /cms/wp-includes/wlwmanifest.xml
Not Found: /favicon.ico
Not Found: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /sitemap.xml
Bad Request: /robots.txt
Not Found: /favicon.ico
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /
Not Found: /robots.txt
Not Found: /favicon.ico
Not Found: /sitemap.xml
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /robots.txt
Bad Request: /sitemap.xml
Not Found: /sitemap.xml
Not Found: /robots.txt
Not Found: /favicon.ico
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Not Found: /favicon.ico
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /robots.txt
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /sitemap.xml
Not Found: /favicon.ico
Not Found: /robots.txt
Not Found: /sitemap.xml
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Not Found: /sitemap.xml
Not Found: /robots.txt
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /sitemap.xml
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /robots.txt
Not Found: /pma/index.php
Not Found: /phpmyadmin/index.php
Not Found: /PhpMyAdmin/index.php
Not Found: /php/thinkphp/aaaffff123.php
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /pma/index.php
Not Found: /index_sso.php
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /phpmyadmin/index.php
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /PhpMyAdmin/index.php
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /php/thinkphp/aaaffff123.php
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /index_sso.php
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /robots.txt
Not Found: /sitemap.xml
Not Found: /sitemap.xml
Not Found: /robots.txt
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /sitemap.xml
Not Found: /robots.txt
Not Found: /sitemap.xml
Not Found: /robots.txt
Not Found: /favicon.ico
Not Found: /robots.txt
Not Found: /favicon.ico
Not Found: /robots.txt
Not Found: /robots.txt
Not Found: /favicon.ico
Not Found: /wordpress
Not Found: /Wordpress
Not Found: /WORDPRESS
Not Found: /WordPress
Not Found: /wp
Not Found: /Wp
Not Found: /WP
Not Found: /old
Not Found: /Old
Not Found: /OLD
Not Found: /oldsite
Not Found: /new
Not Found: /New
Not Found: /NEW
Not Found: /wp-old
Not Found: /2022
Not Found: /2020
Not Found: /2019
Not Found: /2018
Not Found: /backup
Not Found: /test
Not Found: /Test
Not Found: /TEST
Not Found: /demo
Not Found: /bc
Not Found: /www
Not Found: /WWW
Not Found: /Www
Not Found: /2021
Not Found: /main
Not Found: /old-site
Not Found: /bk
Not Found: /Backup
Not Found: /BACKUP
Not Found: /SHOP
Not Found: /Shop
Not Found: /shop
Not Found: /bak
Not Found: /sitio
Not Found: /bac
Not Found: /sito
Not Found: /site
Not Found: /Site
Not Found: /SITE
Not Found: /blog
Not Found: /BLOG
Not Found: /Blog
Not Found: /robots.txt
Not Found: /robots.txt
Not Found: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /
Not Found: /favicon.ico
Not Found: /sitemap.xml
Not Found: /robots.txt
Not Found: /favicon.ico
Not Found: /robots.txt
Not Found: /sitemap.xml
Not Found: /wp-comments-post.php
Not Found: /favicon.ico
Not Found: /favicon.ico
Forbidden (CSRF cookie not set.): /
Forbidden (CSRF cookie not set.): /
Forbidden (CSRF cookie not set.): /
Forbidden (CSRF cookie not set.): /
Not Found: /apple-touch-icon-120x120-precomposed.png
Not Found: /apple-touch-icon-120x120.png
Not Found: /apple-touch-icon-precomposed.png
Not Found: /apple-touch-icon.png
Not Found: /favicon.ico
Not Found: /apple-touch-icon-120x120-precomposed.png
Not Found: /apple-touch-icon-120x120.png
Not Found: /apple-touch-icon-precomposed.png
Not Found: /apple-touch-icon.png
Not Found: /favicon.ico
Not Found: /wordpress
Not Found: /wp
Forbidden (CSRF cookie not set.): /
Forbidden (CSRF cookie not set.): /
Forbidden (CSRF cookie not set.): /
Not Found: /favicon.ico
Not Found: /favicon.ico
Forbidden (CSRF cookie not set.): /
Not Found: /favicon.ico
Forbidden (CSRF token from POST incorrect.): /
Forbidden (CSRF token from POST incorrect.): /
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Not Found: /products.json
Not Found: /wp-json/wc/store/
Not Found: /robots.txt
Not Found: /robots.txt
Not Found: /favicon.ico
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Not Found: /favicon.ico
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /sitemap.xml
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /robots.txt
Bad Request: /sitemap.xml
Not Found: /favicon.ico
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /robots.txt
Not Found: /sitemap.xml
Not Found: /robots.txt
Not Found: /robots.txt
Not Found: /sitemap.xml
Not Found: /robots.txt
Not Found: /wp-includes/wlwmanifest.xml
Not Found: /xmlrpc.php
Not Found: /blog/wp-includes/wlwmanifest.xml
Not Found: /web/wp-includes/wlwmanifest.xml
Not Found: /wordpress/wp-includes/wlwmanifest.xml
Not Found: /website/wp-includes/wlwmanifest.xml
Not Found: /wp/wp-includes/wlwmanifest.xml
Not Found: /news/wp-includes/wlwmanifest.xml
Not Found: /2018/wp-includes/wlwmanifest.xml
Not Found: /2019/wp-includes/wlwmanifest.xml
Not Found: /shop/wp-includes/wlwmanifest.xml
Not Found: /wp1/wp-includes/wlwmanifest.xml
Not Found: /test/wp-includes/wlwmanifest.xml
Not Found: /media/wp-includes/wlwmanifest.xml
Not Found: /wp2/wp-includes/wlwmanifest.xml
Not Found: /site/wp-includes/wlwmanifest.xml
Not Found: /cms/wp-includes/wlwmanifest.xml
Not Found: /sito/wp-includes/wlwmanifest.xml
Not Found: /favicon.ico
Not Found: /robots.txt
Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'mail.codespa.info'. You may need to add 'mail.codespa.info' to ALLOWED_HOSTS.
Bad Request: /
Not Found: /favicon.ico
Not Found: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Not Found: /favicon.ico
[UID:2380][31649] Child process with pid: 31762 was killed by signal: 15, core dumped: no
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /robots.txt
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /static/base/css/style.css
Not Found: /static/base/js/main.js
Not Found: /favicon.ico
Not Found: /static/Base/js/popper.min.js.map
Not Found: /static/Base/js/bootstrap.min.js.map
Not Found: /SELECT%20*%20FROM%20information_schema.tables
Not Found: /favicon.ico
Not Found: /favicon.ico
Forbidden (CSRF cookie not set.): /
Forbidden (CSRF cookie not set.): /
Forbidden (CSRF cookie not set.): /
Forbidden (CSRF cookie not set.): /
Forbidden (CSRF cookie not set.): /
Not Found: /favicon.ico
Forbidden (CSRF cookie not set.): /
Not Found: /favicon.ico
Not Found: /favicon.ico
Forbidden (CSRF cookie not set.): /
Not Found: /favicon.ico
Internal Server Error: /logout/
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home3/rayitode/codespa/Login/views.py", line 49, in logout_out
    del request.session['member_id']
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/contrib/sessions/backends/base.py", line 60, in __delitem__
    del self._session[key]
KeyError: 'member_id'
Not Found: /favicon.ico
Not Found: /robots.txt
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Not Found: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Not Found: /robots.txt
Not Found: /.well-known/security.txt
Not Found: /security.txt
Not Found: /.well-known/security.txt
Not Found: /security.txt
Not Found: /.well-known/security.txt
Not Found: /security.txt
Not Found: /robots.txt
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /robots.txt
Not Found: /robots.txt
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Not Found: /favicon.ico
Forbidden (CSRF cookie not set.): /
Forbidden (CSRF cookie not set.): /
Forbidden (CSRF cookie not set.): /
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /wordpress
Not Found: /Wordpress
Not Found: /WORDPRESS
Not Found: /WordPress
Not Found: /wp
Not Found: /Wp
Not Found: /WP
Not Found: /old
Not Found: /Old
Not Found: /OLD
Not Found: /oldsite
Not Found: /new
Not Found: /New
Not Found: /NEW
Not Found: /wp-old
Not Found: /2022
Not Found: /2020
Not Found: /2019
Not Found: /2018
Not Found: /backup
Not Found: /test
Not Found: /Test
Not Found: /TEST
Not Found: /demo
Not Found: /bc
Not Found: /www
Not Found: /WWW
Not Found: /Www
Not Found: /2021
Not Found: /main
Not Found: /old-site
Not Found: /bk
Not Found: /Backup
Not Found: /BACKUP
Not Found: /SHOP
Not Found: /Shop
Not Found: /shop
Not Found: /bak
Not Found: /sitio
Not Found: /bac
Not Found: /sito
Not Found: /site
Not Found: /Site
Not Found: /SITE
Not Found: /blog
Not Found: /BLOG
Not Found: /Blog
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /robots.txt
Not Found: /robots.txt
Not Found: /favicon.ico
Forbidden (CSRF cookie not set.): /
Not Found: /favicon.ico
Forbidden (CSRF cookie not set.): /
Not Found: /robots.txt
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Not Found: /robots.txt
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Not Found: /favicon.ico
Not Found: /robots.txt
Not Found: /robots.txt
Not Found: /robots.txt
Not Found: /favicon.ico
Not Found: /robots.txt
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Not Found: /robots.txt
Not Found: /humans.txt
Not Found: /ads.txt
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Not Found: /favicon.ico
Not Found: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Internal Server Error: /inicio/admin/
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "/home3/rayitode/codespa/Inicio/views.py", line 7, in inicioadmin
    return render(request,'Inicio/admin.html')
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loader.py", line 62, in render_to_string
    return template.render(context, request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/backends/django.py", line 61, in render
    return self.template.render(context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 175, in render
    return self._render(context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 167, in _render
    return self.nodelist.render(context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loader_tags.py", line 132, in render
    compiled_parent = self.get_parent(context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loader_tags.py", line 129, in get_parent
    return self.find_template(parent, context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loader_tags.py", line 107, in find_template
    template, origin = context.template.engine.find_template(
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loaders/cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loaders/base.py", line 23, in get_template
    contents = self.get_contents(origin)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loaders/cached.py", line 26, in get_contents
    return origin.loader.get_contents(origin)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loaders/filesystem.py", line 23, in get_contents
    return fp.read()
  File "/opt/alt/python39/lib64/python3.9/codecs.py", line 322, in decode
    (result, consumed) = self._buffer_decode(data, self.errors, final)
UnicodeDecodeError: 'utf-8' codec can't decode byte 0x8f in position 12940: invalid start byte
Internal Server Error: /inicio/admin/
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "/home3/rayitode/codespa/Inicio/views.py", line 7, in inicioadmin
    return render(request,'Inicio/admin.html')
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loader.py", line 62, in render_to_string
    return template.render(context, request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/backends/django.py", line 61, in render
    return self.template.render(context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 175, in render
    return self._render(context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 167, in _render
    return self.nodelist.render(context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loader_tags.py", line 132, in render
    compiled_parent = self.get_parent(context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loader_tags.py", line 129, in get_parent
    return self.find_template(parent, context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loader_tags.py", line 107, in find_template
    template, origin = context.template.engine.find_template(
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loaders/cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loaders/base.py", line 23, in get_template
    contents = self.get_contents(origin)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loaders/cached.py", line 26, in get_contents
    return origin.loader.get_contents(origin)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loaders/filesystem.py", line 23, in get_contents
    return fp.read()
  File "/opt/alt/python39/lib64/python3.9/codecs.py", line 322, in decode
    (result, consumed) = self._buffer_decode(data, self.errors, final)
UnicodeDecodeError: 'utf-8' codec can't decode byte 0x8f in position 12940: invalid start byte
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /robots.txt
Not Found: /wp-includes/js/jquery/jquery.js
Not Found: /robots.txt
Not Found: /robots.txt
Not Found: /robots.txt
Not Found: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Not Found: /favicon.ico
Not Found: /robots.txt
Internal Server Error: /persona/lista/
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "/home3/rayitode/codespa/Persona/views.py", line 62, in lista
    return render(request,"Persona/lista.html",{'listado':persona})
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loader.py", line 15, in get_template
    return engine.get_template(template_name)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/backends/django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loaders/cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loaders/base.py", line 28, in get_template
    return Template(
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 200, in compile_nodelist
    return parser.parse()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 484, in parse
    raise self.error(token, e)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 482, in parse
    filter_expression = self.compile_filter(token.contents)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 600, in compile_filter
    return FilterExpression(token, self)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 703, in __init__
    raise TemplateSyntaxError(
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: ' 'd-m-Y'' from 'l.fecha_nac | date 'd-m-Y''
Internal Server Error: /persona/lista/
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "/home3/rayitode/codespa/Persona/views.py", line 62, in lista
    return render(request,"Persona/lista.html",{'listado':persona})
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loader.py", line 15, in get_template
    return engine.get_template(template_name)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/backends/django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loaders/cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loaders/base.py", line 28, in get_template
    return Template(
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 200, in compile_nodelist
    return parser.parse()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 484, in parse
    raise self.error(token, e)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 482, in parse
    filter_expression = self.compile_filter(token.contents)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 600, in compile_filter
    return FilterExpression(token, self)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 703, in __init__
    raise TemplateSyntaxError(
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: ' '%d-%m-%Y'' from 'l.fecha_nac | date '%d-%m-%Y''
Internal Server Error: /persona/lista/
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "/home3/rayitode/codespa/Persona/views.py", line 62, in lista
    return render(request,"Persona/lista.html",{'listado':persona})
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loader.py", line 15, in get_template
    return engine.get_template(template_name)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/backends/django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loaders/cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loaders/base.py", line 28, in get_template
    return Template(
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 200, in compile_nodelist
    return parser.parse()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 484, in parse
    raise self.error(token, e)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 482, in parse
    filter_expression = self.compile_filter(token.contents)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 600, in compile_filter
    return FilterExpression(token, self)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 703, in __init__
    raise TemplateSyntaxError(
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: ' :'%d-%m-%Y'' from 'l.fecha_nac | date :'%d-%m-%Y''
Internal Server Error: /persona/lista/
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "/home3/rayitode/codespa/Persona/views.py", line 62, in lista
    return render(request,"Persona/lista.html",{'listado':persona})
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loader.py", line 15, in get_template
    return engine.get_template(template_name)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/backends/django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loaders/cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loaders/base.py", line 28, in get_template
    return Template(
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 200, in compile_nodelist
    return parser.parse()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 484, in parse
    raise self.error(token, e)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 482, in parse
    filter_expression = self.compile_filter(token.contents)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 600, in compile_filter
    return FilterExpression(token, self)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 703, in __init__
    raise TemplateSyntaxError(
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: ' : 'd-m-Y'' from 'l.fecha_nac | date : 'd-m-Y''
Internal Server Error: /persona/lista/
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "/home3/rayitode/codespa/Persona/views.py", line 59, in lista
    return render(request,"Persona/lista.html",{'listado':persona})
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loader.py", line 62, in render_to_string
    return template.render(context, request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/backends/django.py", line 61, in render
    return self.template.render(context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 175, in render
    return self._render(context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 167, in _render
    return self.nodelist.render(context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 167, in _render
    return self.nodelist.render(context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/defaulttags.py", line 238, in render
    nodelist.append(node.render_annotated(context))
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/urls/base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/urls/resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'ActualizarPersona' not found. 'ActualizarPersona' is not a valid view function or pattern name.
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /wordpress
Not Found: /wp
Not Found: /new
Not Found: /old
Not Found: /blog
Not Found: /test
Not Found: /shop
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /favicon.ico
tesorero
Not Found: /favicon.ico
Not Found: /wp-includes/js/jquery/jquery.js
Not Found: /administrator/help/en-GB/toc.json
Not Found: /administrator/language/en-GB/install.xml
Not Found: /plugins/system/debug/debug.xml
Not Found: /administrator/
Not Found: /misc/ajax.js
Not Found: /images/editor/separator.gif
Not Found: /js/header-rollup-554.js
Not Found: /.env
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Not Found: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Not Found: /robots.txt
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /
Not Found: /robots.txt
Not Found: /robots.txt
Not Found: /robots.txt
Not Found: /robots.txt
Not Found: /robots.txt
Not Found: /favicon.ico
Not Found: /favicon.ico
Not Found: /robots.txt
Not Found: /.ftpconfig
Internal Server Error: /inicio/admin/
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "/home3/rayitode/codespa/Inicio/views.py", line 7, in inicioadmin
    return render(request,'Inicio/admin.html')
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loader.py", line 62, in render_to_string
    return template.render(context, request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/backends/django.py", line 61, in render
    return self.template.render(context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 175, in render
    return self._render(context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 167, in _render
    return self.nodelist.render(context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 167, in _render
    return self.nodelist.render(context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/defaulttags.py", line 321, in render
    return nodelist.render(context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/template/defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/urls/base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/urls/resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'Persona' not found. 'Persona' is not a valid view function or pattern name.
Not Found: /favicon.ico
Internal Server Error: /persona/actualizar/1978265471901
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "/home3/rayitode/codespa/Persona/views.py", line 88, in actualizar
    form = PersonaForm(instance=cliente)
NameError: name 'PersonaForm' is not defined
Internal Server Error: /persona/actualizar/1978265471901
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapped_view
    return view_func(request, *args, **kwargs)
  File "/home3/rayitode/codespa/Persona/views.py", line 88, in actualizar
    form = PersonaForm(instance=cliente)
NameError: name 'PersonaForm' is not defined
Not Found: /favicon.ico
Not Found: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'codespa.info.rayitodesolzacapa.com'. You may need to add 'codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds.php
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /merchant/code
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /3ds1633693954432212
Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/core/handlers/exception.py", line 56, in inner
    response = get_response(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/utils/deprecation.py", line 135, in __call__
    response = self.process_request(request)
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/middleware/common.py", line 48, in process_request
    host = request.get_host()
  File "/home3/rayitode/virtualenv/codespa/3.9/lib/python3.9/site-packages/django/http/request.py", line 152, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'www.codespa.info.rayitodesolzacapa.com'. You may need to add 'www.codespa.info.rayitodesolzacapa.com' to ALLOWED_HOSTS.
Bad Request: /favicon.ico
Not Found: /favicon.ico
Not Found: /favicon.ico
