{% extends 'BaseAdmin/base.html' %}
{% load static %}

{% block content %}

{% if messages %}
{% for message in messages %}
<script>
    Swal.fire({
        "title": "Informacion Sistema",
        "text": "{{message}}",
        "icon": "{{message.tags}}"
    })
</script>
{% endfor %}
{% endif %}
<br><br>
<div class="container">
<a href="{% url 'ListaUser' %}" class="btn btn-info"> Personal Activos</a>
</div>
<main class="col-md-9 ms-sm-auto col-lg-12 px-md-4" style="padding-top: 20px;">
    <div class="container" style="background-color:white;">
        <form action="" method="POST">{% csrf_token %}
            <br>
            <h3 class="text-primary" align="center">REGISTRO DE NUEVO USUARIO</h3><br><br>
            <div class="row" align="center" style="margin: 15px;">
                <div class="col-md-3 col-sm-12">
                    <div class="form-group">
                        <label >{{form.username.label_tag}}</label>
                        {{form.username}}
                    </div>
                </div>
                <div class="col-md-3 col-sm-12">
                    <div class="form-group">
                        <label>{{form.password.label_tag}}</label>
                        {{form.password}}
                    </div>
                </div>
                <div class="col-md-3 col-sm-12">
                    <div class="form-group">
                        <label>{{form.first_name.label_tag}}</label>
                        {{form.first_name}}
                    </div>
                </div>
                <div class="col-md-3 col-sm-12">
                    <div class="form-group">
                        <label>{{form.last_name.label_tag}}</label>
                        {{form.last_name}}
                    </div>
                </div>
            </div>
            <div class="row" align="center" style="margin: 15px;">
                <div class="col-md-3 col-sm-12">
                    <div class="form-group">
                        <label>{{form.email.label_tag}}</label>
                        {{form.email}}
                    </div>
                </div>
                <div class="col-md-3 col-sm-12">
                    <div class="form-group">
                        <label>{{form.rol.label_tag}}</label>
                        {{form.rol}}
                    </div>
                </div>
                <div class="col-md-3 col-sm-12">
                    <div class="form-group">
                        <label>{{form.is_staff.label_tag}}</label>
                        {{form.is_staff}}
                    </div>
                </div>
                <div class="col-md-3 col-sm-12">
                    <div class="form-group">
                        <label>{{form.is_active.label_tag}}</label>
                        {{form.is_active}}
                    </div>
                </div>
            </div>
            <div class="row" align="center" style="margin: 15px;">
                <div class="col-md-3 col-sm-12">
                    <div class="form-group">
                        <label>{{form.is_superuser.label_tag}}</label>
                        {{form.is_superuser}}
                    </div>
                </div>
            </div>
            <br>
            <div class="container" align="center">
                <button type="submit" class="btn btn-success">Guardar</button>
                <button type="reset" class="btn btn-danger">Cancelar</button>
            </div>
            <br>
        </form>
    </div>
</main>
<br><br><br><br>

{% endblock %}