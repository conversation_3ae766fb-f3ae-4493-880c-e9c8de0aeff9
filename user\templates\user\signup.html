{% extends 'BaseLogin/login.html' %}
{% block content %}
{% if messages %}
{% for message in messages %}
<script>
    Swal.fire({
        "title": "Información Sistema",
        "text": "{{ message }}",
        "icon": "{{ message.tags }}"
    })
</script>
{% endfor %}
{% endif %}

<div class="container-fluid position-relative d-flex p-0">
    <!-- Spinner Start -->
    <div id="spinner" class="show bg-dark position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
        <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
            <span class="sr-only">Cargando...</span>
        </div>
    </div>
    <!-- Spinner End -->

    <!-- Sign Up Start -->
    <div class="container-fluid">
        <div class="row h-100 align-items-center justify-content-center" style="min-height: 100vh;">
            <!-- Aumentamos el tamaño del formulario para que ocupe más ancho -->
            <div class="col-12 col-sm-12 col-md-10 col-lg-8 col-xl-7">
                <div class="bg-secondary rounded p-4 p-sm-5 my-4 mx-3" style="min-height: 500px;">
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <h3 class="text-primary"><i class="fa fa-user-edit me-2"></i>Sagastume</h3>
                    </div>
                    <h3 class="text-center text-white mb-4">Registro</h3>

                    <form action="#" method="POST">{% csrf_token %}
                        <div class="row">
                            <!-- Columna 1 -->
                            <div class="col-md-4 mb-3">
                                <div class="form-floating">
                                    <label for="floatingText" class="form-label">Usuario</label>
                                    <input type="text" name="username" class="form-control" id="floatingText" placeholder="Usuario">
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-floating">
                                    <label for="floatingPassword" class="form-label">Clave</label>
                                    <input type="password" name="password" class="form-control" id="floatingPassword" placeholder="Clave">
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-floating">
                                    <label for="floatingFirstName" class="form-label">Nombre</label>
                                    <input type="text" name="first_name" class="form-control" id="floatingFirstName" placeholder="Nombre">
                                </div>
                            </div>

                            <!-- Columna 2 -->
                            <div class="col-md-4 mb-3">
                                <div class="form-floating">
                                    <label for="floatingLastName" class="form-label">Apellido</label>
                                    <input type="text" name="last_name" class="form-control" id="floatingLastName" placeholder="Apellido">
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-floating">
                                    <label for="floatingTelefono" class="form-label">Teléfono (Opcional)</label>
                                    <input type="text" name="telefono" class="form-control" id="floatingTelefono" placeholder="Teléfono ">
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-floating">
                                    <label for="floatingNit" class="form-label">NIT (Opcional)</label>
                                    <input type="text" name="nit" class="form-control" id="floatingNit" placeholder="NIT ">
                                </div>
                            </div>

                            <!-- Columna 3 -->
                            <div class="col-md-4 mb-3">
                                <div class="form-floating">
                                    <label for="floatingEmail" class="form-label">Correo Electrónico (Opcional)</label>
                                    <input type="email" name="email" class="form-control" id="floatingEmail" placeholder="Correo electrónico ">
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-floating">
                                    <label for="floatingDireccion" class="form-label">Dirección de Domicilio (Opcional)</label>
                                    <input type="text" name="direccion" class="form-control" id="floatingDireccion" placeholder="Dirección de Domicilio ">
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="form-floating">
                                    <label for="floatingFechaNacimiento" class="form-label">Fecha de Nacimiento</label>
                                    <input type="date" name="fecha_nac" class="form-control" id="floatingFechaNacimiento" placeholder="Fecha de Nacimiento">
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary py-3 w-100 mb-4">Registrarme</button>
                    </form>

                    <p class="text-center mb-0">¿Ya tengo usuario? <a href="{% url 'Login' %}">Ingresar</a></p>

                    <div class="text-center mt-4">
                        <span>Diseñado por </span>
                        <a href="#">MultiServicios Sagastume</a><br>
                        <span>Derechos Reservados 2023</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Sign Up End -->
</div>

{% endblock %}
