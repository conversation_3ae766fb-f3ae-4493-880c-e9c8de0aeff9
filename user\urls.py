from user import views
from django.urls import path
from django.conf import settings
from django.conf.urls.static import static 

urlpatterns = [
    path('nuevousuario/',views.nuevousuario,name="NuevoUser"),
    path('listausuario/',views.listausuario,name="ListaUser"),
    path('listausuario2/',views.listausuario2,name="ListaUser2"),
    path('listatrabajador/',views.listatrabajador,name="ListaTrabajador"),
    path('updateusuario/<str:id>',views.updateusuario,name="UpdateUser"),
    path('deleteusuario/<str:id>',views.deleteusuario,name="DeleteUser"),
    path('registro/',views.login_signup,name="<PERSON><PERSON>"),
]

urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)