from django.shortcuts import render,redirect
from datetime import datetime
from django.contrib import messages
from django.contrib.auth.decorators import login_required
#from Categorias.models import Categoria
from user.models import User
#from Cliente.models import Cliente
#from Proveedor.models import Proveedor
#from Producto.models import Producto

@login_required
def inicio(request):
      if not request.user.is_authenticated and not request.user.is_active:
        return redirect('/')
      else: 
        #totalcategorias=Categoria.objects.count()
        totalusuarios=User.objects.count()
       # totalclientes=Cliente.objects.count()
       # totalproveedor=Proveedor.objects.count()
       # totalproductos=Producto.objects.count()
        return render(request,'Admin/inicio.html',{'usuarios':totalusuarios,})