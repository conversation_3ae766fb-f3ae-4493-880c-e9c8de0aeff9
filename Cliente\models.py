from django.db import models
from user.models import User
import uuid


class Cliente(models.Model):
    id_cliente = models.BigAutoField(primary_key=True,blank=False,null=False)
    tipo = models.CharField(max_length=100,blank=False,null=False)
    nit = models.CharField(max_length=250,blank=True,null=True,default="CF")
    nombre = models.CharField(max_length=500,blank=False,null=False)
    apellido = models.CharField(max_length=850,blank=False,null=False)
    direccion = models.CharField(max_length=1500,blank=False,null=False)
    telefono = models.CharField(max_length=9,blank=False,null=False)
    fecha_nac = models.DateField(blank=True,null=True,default='')
    compras = models.IntegerField(blank=True,null=True,default=0)
    total_compras = models.DecimalField(max_digits=12,decimal_places=2,blank=True,null=True,default=0.00)
    devoluciones = models.IntegerField(blank=True,null=True,default=0)
    total_devoluciones = models.DecimalField(max_digits=12,decimal_places=2,blank=True,null=True,default=0.00)
    descuento_empleado = models.IntegerField(blank=True,null=True,default=0)
    estado = models.IntegerField(blank=False,null=False,default=1)
    fecha = models.DateField(blank=False,null=False)
    usuario = models.ForeignKey(User,on_delete=models.CASCADE,blank=False,null=False)
    token = models.UUIDField(default=uuid.uuid4, editable=False)

    class Meta:
        ordering = ["id_cliente"]

    def __str__(self):
        return str(self.id_cliente)

