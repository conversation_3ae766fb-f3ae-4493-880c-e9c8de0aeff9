# Generated by Django 4.2.6 on 2023-11-10 04:03

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Cliente',
            fields=[
                ('id_cliente', models.BigAutoField(primary_key=True, serialize=False)),
                ('tipo', models.CharField(max_length=100)),
                ('nit', models.CharField(blank=True, default='CF', max_length=250, null=True)),
                ('nombre', models.CharField(max_length=500)),
                ('apellido', models.CharField(max_length=850)),
                ('direccion', models.CharField(max_length=1500)),
                ('telefono', models.CharField(max_length=9)),
                ('fecha_nac', models.DateField(blank=True, default='', null=True)),
                ('compras', models.IntegerField(blank=True, default=0, null=True)),
                ('total_compras', models.DecimalField(blank=True, decimal_places=2, default=0.0, max_digits=12, null=True)),
                ('devoluciones', models.IntegerField(blank=True, default=0, null=True)),
                ('total_devoluciones', models.DecimalField(blank=True, decimal_places=2, default=0.0, max_digits=12, null=True)),
                ('descuento_empleado', models.IntegerField(blank=True, default=0, null=True)),
                ('estado', models.IntegerField(default=1)),
                ('fecha', models.DateField()),
                ('token', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('usuario', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['id_cliente'],
            },
        ),
    ]
