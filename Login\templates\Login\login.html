{% extends 'BaseLogin/login.html' %}
{% block content %}
{% if messages %}
{% for message in messages %}
<script>
    Swal.fire({
        "title": "Información Sistema",
        "text": "{{ message }}",
        "icon": "{{ message.tags }}"
    })
</script>
{% endfor %}
{% endif %}

<div class="container-fluid position-relative d-flex p-0">
    <!-- Spinner Start -->
    <div id="spinner" class="show bg-dark position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
        <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>
    <!-- Spinner End -->

    <!-- Sign In Start -->
    <div class="container-fluid">
        <div class="row h-150 align-items-center justify-content-center" style="min-height: 100vh;">
            <div class="col-12 col-sm-8 col-md-6 col-lg-5 col-xl-4">
                <div class="bg-secondary rounded p-4 p-sm-5 my-4 mx-3">
                    <div class="d-flex align-items-center justify-content-between mb-3">
                        <a href="index.html" class="">
                            <h3 class="text-primary"><i class="fa fa-user-edit me-2"></i></h3>
                        </a>
                    </div>
                    <!-- Encabezado centrado -->
                    <h3 class="text-center">Ingreso</h3>

                    <form action="#" method="POST" enctype="multipart/form-data">{% csrf_token %}
                        <div class="form-floating mb-3">
                            <label for="floatingInput" class="fa fa-user-edit me-2 btn-secondary">Usuario</label><br>
                            {{ form.username }}
                        </div>
                        <div class="form-floating mb-4">
                            <label for="floatingPassword" class="fa fa-user-edit me-2 btn-secondary">Clave</label><br>
                            {{ form.password }}
                        </div>
                        <button type="submit" class="btn btn-primary py-3 w-100 mb-4">Ingresar</button>
                    </form>

                    <p class="text-center mb-0">No tienes Usuario? <a href="{% url 'Registro' %}"> Registrarme</a></p>

                    <p class="text-center">
                        <br>
                        <span>Diseñado por </span>
                        <a href="#">
                            <span>MultiServicios Sagastume</span>
                        </a><br>
                        <span>Derechos Reservados 2023</span>
                    </p>
                </div>
            </div>
        </div>
    </div>
    <!-- Sign In End -->
</div>

{% endblock %}
