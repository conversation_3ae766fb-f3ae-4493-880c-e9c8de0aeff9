from django.shortcuts import render,redirect
from datetime import datetime
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from Cliente.models import Cliente
from user.models import User


@login_required
def nuevo(request):
    pass
    #para crear usuarios desde dashboard


@login_required
def nuevocliente():
    pass


def listacliente(request):
    # Verificar si el usuario está autenticado, activo y tiene rol de admin
    if not request.user.is_authenticated or not request.user.is_active or request.user.rol != 'admin':
        return redirect('Inicio')

    # Filtrar usuarios por rol de 'cliente' y que estén activos
    usuarios = User.objects.filter(is_active=True, rol='cliente')
    
    return render(request, "Cliente/lista.html", {'usuarios': usuarios})


def listacliente2(request):
    # Verificar si el usuario está autenticado, activo y tiene rol de admin
    if not request.user.is_authenticated or not request.user.is_active or request.user.rol != 'admin':
        return redirect('Inicio')

    # Filtrar usuarios por rol de 'cliente' y que estén activos
    usuarios = User.objects.filter(is_active=0, rol='cliente')
    
    return render(request, "Cliente/lista2.html", {'usuarios': usuarios})